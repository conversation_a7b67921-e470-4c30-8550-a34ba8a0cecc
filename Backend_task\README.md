# Water Suppliers API

REST API for managing water suppliers with JWT authentication.

## Setup

```bash
npm install
npm start
```

Default credentials: `admin` / `password`

## API Endpoints

### Authentication
```
POST /api/auth/login
{
  "username": "admin",
  "password": "password"
}
```

### Suppliers (requires Bearer token)
```
GET    /api/suppliers      - Get all suppliers
GET    /api/suppliers/:id  - Get supplier by ID
POST   /api/suppliers      - Create supplier
PUT    /api/suppliers/:id  - Update supplier
DELETE /api/suppliers/:id  - Delete supplier
```

### Sample Supplier Data
```json
{
  "name": "WaterCorp Ltd",
  "contact": "<PERSON>",
  "address": "456 Supply Ave",
  "phone": "555-0123",
  "email": "<EMAIL>"
}
```

## Project Structure
```
├── config/database.js      - Data storage
├── controllers/            - Business logic
├── middleware/auth.js      - JWT authentication
├── models/Supplier.js      - Data validation
├── routes/                 - API routes
└── server.js              - Main application
```

## Testing with curl

Login:
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

Create supplier:
```bash
curl -X POST http://localhost:3000/api/suppliers \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"WaterCorp","contact":"John Doe"}'
```