const database = require('../config/database');
const Supplier = require('../models/Supplier');

const getAllSuppliers = (req, res) => {
  try {
    const suppliers = database.getAllSuppliers();
    const message = suppliers.length === 0 ? 'No suppliers found' : `Found ${suppliers.length} supplier${suppliers.length === 1 ? '' : 's'}`;
    res.json({
      success: true,
      message: message,
      data: suppliers,
      count: suppliers.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch suppliers'
    });
  }
};

const getSupplierById = (req, res) => {
  try {
    const { id } = req.params;
    const supplier = database.getSupplierById(id);
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    res.json({
      success: true,
      message: 'Supplier retrieved successfully',
      data: supplier
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch supplier'
    });
  }
};

const createSupplier = (req, res) => {
  try {
    const supplierData = req.body;
    
    // Validate input
    const validationErrors = Supplier.validate(supplierData);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validationErrors
      });
    }

    const supplier = new Supplier(supplierData);
    const createdSupplier = database.createSupplier(supplier);

    res.status(201).json({
      success: true,
      message: 'Supplier created successfully',
      data: createdSupplier
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to create supplier'
    });
  }
};

const updateSupplier = (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Check if supplier exists
    const existingSupplier = database.getSupplierById(id);
    if (!existingSupplier) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    // Validate update data
    const validationErrors = Supplier.validate({ ...existingSupplier, ...updateData });
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validationErrors
      });
    }

    const updatedSupplier = database.updateSupplier(id, updateData);

    res.json({
      success: true,
      message: 'Supplier updated successfully',
      data: updatedSupplier
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to update supplier'
    });
  }
};

const deleteSupplier = (req, res) => {
  try {
    const { id } = req.params;
    
    const deleted = database.deleteSupplier(id);
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Supplier not found'
      });
    }

    res.json({
      success: true,
      message: 'Supplier deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to delete supplier'
    });
  }
};

module.exports = {
  getAllSuppliers,
  getSupplierById,
  createSupplier,
  updateSupplier,
  deleteSupplier
};