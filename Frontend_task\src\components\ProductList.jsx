import React from 'react'
import ProductCard from './ProductCard'
import './ProductList.css'

const ProductList = ({ products, loading, error, onAddToCart }) => {
  if (loading) {
    return (
      <div className="product-list">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading products...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="product-list">
        <div className="error">
          <p>{error}</p>
        </div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="product-list">
        <div className="no-products">
          <p>No products found matching your search.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="product-list">
      <div className="products-grid">
        {products.map(product => (
          <ProductCard
            key={product.id}
            product={product}
            onAddToCart={onAddToCart}
          />
        ))}
      </div>
    </div>
  )
}

export default ProductList
