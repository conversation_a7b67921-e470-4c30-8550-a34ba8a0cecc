// In-memory database simulation
class Database {
  constructor() {
    this.suppliers = [];
    this.users = [
      { 
        id: 1, 
        username: 'admin', 
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
      }
    ];
    this.supplierIdCounter = 1;
  }

  // Supplier methods
  getAllSuppliers() {
    return this.suppliers;
  }

  getSupplierById(id) {
    return this.suppliers.find(s => s.id === parseInt(id));
  }

  createSupplier(supplierData) {
    const supplier = {
      id: this.supplierIdCounter++,
      ...supplierData,
      createdAt: new Date().toISOString()
    };
    this.suppliers.push(supplier);
    return supplier;
  }

  updateSupplier(id, updateData) {
    const index = this.suppliers.findIndex(s => s.id === parseInt(id));
    if (index === -1) return null;
    
    this.suppliers[index] = {
      ...this.suppliers[index],
      ...updateData,
      updatedAt: new Date().toISOString()
    };
    return this.suppliers[index];
  }

  deleteSupplier(id) {
    const index = this.suppliers.findIndex(s => s.id === parseInt(id));
    if (index === -1) return false;
    
    this.suppliers.splice(index, 1);
    return true;
  }

  // User methods
  getUserByUsername(username) {
    return this.users.find(u => u.username === username);
  }

  createUser(userData) {
    const user = {
      id: this.users.length + 1,
      ...userData,
      createdAt: new Date().toISOString()
    };
    this.users.push(user);
    return user;
  }
}

module.exports = new Database();