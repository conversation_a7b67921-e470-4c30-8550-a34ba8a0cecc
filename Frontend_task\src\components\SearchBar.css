/* SearchBar.css */
.search-bar {
  margin-bottom: 2rem;
}

.search-input-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e1e5e9;
  border-radius: 50px;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1.2rem;
  pointer-events: none;
}

.clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-input-container {
    max-width: 100%;
  }
  
  .search-input {
    padding: 0.875rem 0.875rem 0.875rem 2.75rem;
    font-size: 0.95rem;
  }
  
  .search-icon {
    left: 0.875rem;
    font-size: 1.1rem;
  }
  
  .clear-search {
    right: 0.875rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .search-bar {
    margin-bottom: 1.5rem;
  }
  
  .search-input {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 0.9rem;
  }
  
  .search-icon {
    left: 0.75rem;
    font-size: 1rem;
  }
  
  .clear-search {
    right: 0.75rem;
    font-size: 1rem;
  }
}
