/* ProductList.css */
.product-list {
  width: 100%;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.1rem;
  margin: 0;
}

.error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #dc2626;
  text-align: center;
}

.error p {
  font-size: 1.1rem;
  margin: 0;
  background: #fef2f2;
  padding: 1rem 2rem;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

.no-products {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
  text-align: center;
}

.no-products p {
  font-size: 1.1rem;
  margin: 0;
  background: #f9fafb;
  padding: 1rem 2rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .loading,
  .error,
  .no-products {
    padding: 3rem 1rem;
  }
  
  .loading p,
  .error p,
  .no-products p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .loading,
  .error,
  .no-products {
    padding: 2rem 1rem;
  }
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
  }
}
