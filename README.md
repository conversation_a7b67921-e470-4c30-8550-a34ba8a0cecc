# Developer Technical Assessment

This repository contains both frontend and backend implementations for a technical assessment, demonstrating full-stack development capabilities with modern web technologies.

## 📁 Project Structure

```
├── Frontend_task/          # React Product Store Application
├── Backend_task/           # Node.js Water Suppliers API
├── Demo/                   # Demo videos and screenshots
└── README.md              # This file
```

## 🎯 Assessment Overview

### Frontend Task - React Product Store
A responsive single-page application built with React that provides an e-commerce shopping experience.

**Key Features:**
- Product catalog with real-time search
- Shopping cart with quantity management
- Responsive design for all devices
- Modern UI with smooth animations
- Integration with Fake Store API

### Backend Task - Water Suppliers API
A RESTful API built with Node.js and Express for managing water supplier data with JWT authentication.

**Key Features:**
- JWT-based authentication system
- Full CRUD operations for suppliers
- Input validation and error handling
- RESTful API design principles
- Comprehensive API documentation

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd Frontend_task
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and visit: `http://localhost:5173`

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd Backend_task
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the server:
   ```bash
   npm start
   ```
   
   For development with auto-reload:
   ```bash
   npm run dev
   ```

4. The API will be available at: `http://localhost:3000`

## 🔧 Development Approach

### Frontend Development Strategy

**Technology Choices:**
- **React 19** with hooks for modern component development
- **Vite** for fast development and optimized builds
- **CSS3** with custom responsive design (no external UI libraries)
- **Component-based architecture** for maintainability

**Key Design Decisions:**
- Mobile-first responsive design approach
- Real-time search with debounced filtering
- Persistent cart state during session
- Accessible design with proper ARIA labels
- Modern gradient-based visual design

**Code Organization:**
- Separation of concerns with dedicated service layer
- Reusable components with clear prop interfaces
- Custom CSS modules for styling isolation
- Error boundaries and loading states

### Backend Development Strategy

**Technology Choices:**
- **Node.js** with Express.js for robust API development
- **JWT** for stateless authentication
- **bcryptjs** for secure password hashing
- **In-memory storage** for simplicity (easily replaceable with database)

**Key Design Decisions:**
- RESTful API design following HTTP conventions
- Middleware-based architecture for cross-cutting concerns
- Input validation at model level
- Comprehensive error handling and status codes
- CORS enabled for frontend integration

**Security Considerations:**
- JWT token-based authentication
- Password hashing with bcrypt
- Input sanitization and validation
- Proper HTTP status codes and error messages

## 📋 API Documentation

### Authentication Endpoints

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "message": "Login successful"
}
```

### Supplier Endpoints (Requires Authentication)

All supplier endpoints require the `Authorization: Bearer <token>` header.

#### Get All Suppliers
```http
GET /api/suppliers
Authorization: Bearer <your-jwt-token>
```

#### Get Supplier by ID
```http
GET /api/suppliers/:id
Authorization: Bearer <your-jwt-token>
```

#### Create Supplier
```http
POST /api/suppliers
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "WaterCorp Ltd",
  "contact": "Jane Smith",
  "address": "456 Supply Ave",
  "phone": "555-0123",
  "email": "<EMAIL>"
}
```

#### Update Supplier
```http
PUT /api/suppliers/:id
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "Updated WaterCorp Ltd",
  "contact": "John Doe"
}
```

#### Delete Supplier
```http
DELETE /api/suppliers/:id
Authorization: Bearer <your-jwt-token>
```

## 🧪 Testing

### Frontend Testing
The frontend can be tested by:
1. Starting the development server (`npm run dev`)
2. Testing responsive design by resizing browser window
3. Testing search functionality with various product names
4. Testing cart operations (add, remove, quantity changes)

### Backend Testing

#### Using curl

1. **Login to get token:**
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

2. **Create a supplier:**
```bash
curl -X POST http://localhost:3000/api/suppliers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"name":"AquaSupply Co","contact":"Mike Johnson","phone":"555-0199"}'
```

3. **Get all suppliers:**
```bash
curl -X GET http://localhost:3000/api/suppliers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Using Postman
Demo screenshots are available in the `Demo/Backend_Postman_imgs/` directory showing:
- Authentication login
- CRUD operations for suppliers
- Error handling scenarios

## 🎥 Demo

- **Frontend Demo:** `Demo/Frontend_demo.mp4` - Video demonstration of the React application
- **Backend Demo:** `Demo/Backend_Postman_imgs/` - Postman screenshots showing API functionality

## 🏗️ Architecture Decisions

### Frontend Architecture
- **Component Hierarchy:** App → Header/SearchBar/ProductList/Cart → ProductCard/CartItem
- **State Management:** React hooks (useState, useEffect) for local state
- **API Integration:** Dedicated service layer for external API calls
- **Styling:** CSS modules with responsive breakpoints (480px, 768px, 1024px)

### Backend Architecture
- **Layered Architecture:** Routes → Controllers → Models → Database
- **Middleware Pattern:** Authentication, CORS, and error handling
- **Data Storage:** In-memory arrays (easily replaceable with database)
- **Authentication:** Stateless JWT tokens with configurable expiration

## 🔄 Future Enhancements

### Frontend
- Add product categories filtering
- Implement user authentication
- Add product reviews and ratings
- Integrate with real payment gateway
- Add product comparison feature

### Backend
- Database integration (MongoDB/PostgreSQL)
- User management system
- File upload for supplier documents
- Advanced search and filtering
- Rate limiting and API versioning

## 👨‍💻 Development Notes

This project demonstrates:
- **Full-stack development** capabilities
- **Modern JavaScript** (ES6+) usage
- **RESTful API** design principles
- **Responsive web design** implementation
- **Security best practices** for web applications
- **Clean code** organization and documentation

## 📞 Support

For questions or issues, please refer to the individual README files in each project directory:
- Frontend: `Frontend_task/README.md`
- Backend: `Backend_task/README.md`
