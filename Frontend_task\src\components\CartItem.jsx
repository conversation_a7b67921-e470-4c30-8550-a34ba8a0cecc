import React from 'react'
import './CartItem.css'

const CartItem = ({ item, onRemove, onUpdateQuantity }) => {
  const { id, title, price, image, quantity } = item

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 0) {
      onUpdateQuantity(id, newQuantity)
    }
  }

  const handleRemove = () => {
    onRemove(id)
  }

  const truncateTitle = (text, maxLength = 40) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <div className="cart-item">
      <div className="cart-item-image">
        <img src={image} alt={title} />
      </div>
      
      <div className="cart-item-details">
        <h4 className="cart-item-title" title={title}>
          {truncateTitle(title)}
        </h4>
        <div className="cart-item-price">
          ${price.toFixed(2)} each
        </div>
      </div>
      
      <div className="cart-item-controls">
        <div className="quantity-controls">
          <button 
            onClick={() => handleQuantityChange(quantity - 1)}
            className="quantity-btn"
            aria-label="Decrease quantity"
          >
            -
          </button>
          <span className="quantity-display">{quantity}</span>
          <button 
            onClick={() => handleQuantityChange(quantity + 1)}
            className="quantity-btn"
            aria-label="Increase quantity"
          >
            +
          </button>
        </div>
        
        <div className="cart-item-total">
          ${(price * quantity).toFixed(2)}
        </div>
        
        <button 
          onClick={handleRemove}
          className="remove-btn"
          aria-label={`Remove ${title} from cart`}
        >
          🗑️
        </button>
      </div>
    </div>
  )
}

export default CartItem
