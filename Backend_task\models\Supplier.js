class Supplier {
  constructor(data) {
    this.name = data.name;
    this.contact = data.contact;
    this.address = data.address || '';
    this.phone = data.phone || '';
    this.email = data.email || '';
  }

  static validate(data) {
    const errors = [];
    
    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }
    
    if (!data.contact || data.contact.trim() === '') {
      errors.push('Contact is required');
    }

    if (data.email && !this.isValidEmail(data.email)) {
      errors.push('Invalid email format');
    }

    return errors;
  }

  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = Supplier;