const http = require('http');

const baseUrl = 'http://localhost:3000';
let authToken = '';

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testEndpoints() {
  console.log('Testing Water Suppliers API Endpoints\n');

  try {
    // Test 1: Health Check
    console.log('1️ Testing Health Check...');
    const health = await makeRequest('GET', '/api/health');
    console.log(`Status: ${health.status}`);
    console.log(`Response:`, health.data);
    console.log('');

    // Test 2: Register New User
    console.log('2 Testing User Registration...');
    const register = await makeRequest('POST', '/api/auth/register', {
      username: 'testuser',
      password: 'testpass123'
    });
    console.log(`Status: ${register.status}`);
    console.log(`Response:`, register.data);
    console.log('');

    // Test 3: Login with Admin
    console.log('3️ Testing Login (Admin)...');
    const login = await makeRequest('POST', '/api/auth/login', {
      username: 'admin',
      password: 'password'
    });
    console.log(`Status: ${login.status}`);
    console.log(`Response:`, login.data);
    
    if (login.data.token) {
      authToken = login.data.token;
      console.log(' Token saved for authenticated requests');
    }
    console.log('');

    // Test 4: Get All Suppliers (Empty)
    console.log('4️ Testing Get All Suppliers (Empty)...');
    const emptySuppliers = await makeRequest('GET', '/api/suppliers', null, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${emptySuppliers.status}`);
    console.log(`Response:`, emptySuppliers.data);
    console.log('');

    // Test 5: Create First Supplier
    console.log('5️ Testing Create Supplier...');
    const createSupplier1 = await makeRequest('POST', '/api/suppliers', {
      name: 'AquaFlow Ltd',
      contact: 'John Doe',
      address: '123 Water Street',
      phone: '+1234567890',
      email: '<EMAIL>'
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${createSupplier1.status}`);
    console.log(`Response:`, createSupplier1.data);
    console.log('');

    // Test 6: Create Second Supplier
    console.log('6️ Testing Create Another Supplier...');
    const createSupplier2 = await makeRequest('POST', '/api/suppliers', {
      name: 'WaterCorp Inc',
      contact: 'Jane Smith',
      address: '456 Supply Avenue',
      phone: '+0987654321',
      email: '<EMAIL>'
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${createSupplier2.status}`);
    console.log(`Response:`, createSupplier2.data);
    console.log('');

    // Test 7: Get All Suppliers (With Data)
    console.log('7️ Testing Get All Suppliers (With Data)...');
    const allSuppliers = await makeRequest('GET', '/api/suppliers', null, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${allSuppliers.status}`);
    console.log(`Response:`, allSuppliers.data);
    console.log('');

    // Test 8: Get Supplier by ID
    console.log('8️ Testing Get Supplier by ID...');
    const supplierById = await makeRequest('GET', '/api/suppliers/1', null, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${supplierById.status}`);
    console.log(`Response:`, supplierById.data);
    console.log('');

    // Test 9: Update Supplier
    console.log('9️ Testing Update Supplier...');
    const updateSupplier = await makeRequest('PUT', '/api/suppliers/1', {
      name: 'AquaFlow Limited (Updated)',
      phone: '+1111111111'
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${updateSupplier.status}`);
    console.log(`Response:`, updateSupplier.data);
    console.log('');

    // Test 10: Test Validation Error
    console.log('10Testing Validation Error...');
    const validationError = await makeRequest('POST', '/api/suppliers', {
      name: '',
      contact: ''
    }, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${validationError.status}`);
    console.log(`Response:`, validationError.data);
    console.log('');

    // Test 11: Test Unauthorized Access
    console.log('1️1️ Testing Unauthorized Access...');
    const unauthorized = await makeRequest('GET', '/api/suppliers');
    console.log(`Status: ${unauthorized.status}`);
    console.log(`Response:`, unauthorized.data);
    console.log('');

    // Test 12: Delete Supplier
    console.log('1️2️ Testing Delete Supplier...');
    const deleteSupplier = await makeRequest('DELETE', '/api/suppliers/2', null, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${deleteSupplier.status}`);
    console.log(`Response:`, deleteSupplier.data);
    console.log('');

    // Test 13: Get Non-existent Supplier
    console.log('1️3️ Testing Get Non-existent Supplier...');
    const notFound = await makeRequest('GET', '/api/suppliers/999', null, {
      'Authorization': `Bearer ${authToken}`
    });
    console.log(`Status: ${notFound.status}`);
    console.log(`Response:`, notFound.data);
    console.log('');

    console.log(' All endpoint tests completed!');

  } catch (error) {
    console.error(' Test failed:', error.message);
  }
}

// Run tests
testEndpoints();