import React from 'react'
import CartItem from './CartItem'
import './Cart.css'

const Cart = ({ items, onRemoveItem, onUpdateQuantity, totalCost }) => {
  const itemCount = items.reduce((total, item) => total + item.quantity, 0)

  return (
    <div className="cart">
      <div className="cart-header">
        <h2 className="cart-title">
          <span className="cart-icon">🛒</span>
          Shopping Cart
        </h2>
        <div className="cart-count">
          {itemCount} {itemCount === 1 ? 'item' : 'items'}
        </div>
      </div>

      <div className="cart-content">
        {items.length === 0 ? (
          <div className="empty-cart">
            <div className="empty-cart-icon">🛒</div>
            <p>Your cart is empty</p>
            <p className="empty-cart-subtitle">Add some products to get started!</p>
          </div>
        ) : (
          <>
            <div className="cart-items">
              {items.map(item => (
                <CartItem
                  key={item.id}
                  item={item}
                  onRemove={onRemoveItem}
                  onUpdateQuantity={onUpdateQuantity}
                />
              ))}
            </div>
            
            <div className="cart-summary">
              <div className="cart-total">
                <div className="total-label">Total:</div>
                <div className="total-amount">${totalCost.toFixed(2)}</div>
              </div>
              <button className="checkout-btn">
                Proceed to Checkout
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default Cart
