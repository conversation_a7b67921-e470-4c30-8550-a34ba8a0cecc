# Product Store Application

A responsive React single-page application for browsing and shopping products.

## Features

- **Product Catalog**: Browse products fetched from Fake Store API
- **Search Functionality**: Real-time search to filter products by name
- **Shopping Cart**: Add, remove, and manage product quantities
- **Responsive Design**: Mobile-friendly interface that works on all devices
- **Modern UI**: Clean, professional design with smooth animations

## Technology Stack

- **React 19** - Modern React with hooks
- **Vite** - Fast build tool and development server
- **CSS3** - Custom responsive styling
- **Fake Store API** - Product data source

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the development server:
   ```bash
   npm run dev
   ```

3. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # React components
│   ├── Header.jsx      # Application header
│   ├── SearchBar.jsx   # Product search
│   ├── ProductList.jsx # Product grid
│   ├── ProductCard.jsx # Individual product
│   ├── Cart.jsx        # Shopping cart
│   └── CartItem.jsx    # Cart item
├── services/           # API services
│   └── api.js         # Fake Store API integration
├── App.jsx            # Main application component
└── main.jsx           # Application entry point
```
