/**
 * @fileoverview Minimal environment for bcrypt.js.
 * @externs
 */

/**
 * @param {string} moduleName
 * returns {*}
 */
function require(moduleName) {}

/**
 * @constructor
 * @private
 */
var Module = function() {};

/**
 * @type {*}
 */
Module.prototype.exports;

/**
 * @type {Module}
 */
var module;

/**
 * @type {string}
 */
var __dirname;

/**
 * @type {Object.<string,*>}
 */
var process = {};

/**
 * @param {function()} func
 */
process.nextTick = function(func) {};

/**
 * @param {string} s
 * @constructor
 * @extends Array
 */
var Buffer = function(s) {};

/**
 BEGIN_NODE_INCLUDE
 var crypto = require('crypto');
 END_NODE_INCLUDE
 */

/**
 * @type {Object.<string,*>}
 */
var crypto = {};

/**
 * @param {number} n
 * @returns {Array.<number>}
 */
crypto.randomBytes = function(n) {};

/**
 * @type {Object.<string,*>}
 */
window.crypto = {};

/**
 * @param {Uint8Array|Int8Array|Uint16Array|Int16Array|Uint32Array|Int32Array} array
 */
window.crypto.getRandomValues = function(array) {};

/**
 * @param {string} name
 * @param {function(...[*]):*} constructor
 */
var define = function(name, constructor) {};

/**
 * @type {boolean}
 */
define.amd;

/**
 * @param {...*} var_args
 * @returns {string}
 */
String.fromCodePoint = function(var_args) {};

/**
 * @param {number} offset
 * @returns {number}
 */
String.prototype.codePointAt = function(offset) {};
