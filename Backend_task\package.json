{"name": "water-suppliers-api", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "wa<PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "description": "REST API for managing water suppliers", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}