import React from 'react'
import './ProductCard.css'

const ProductCard = ({ product, onAddToCart }) => {
  const { id, title, price, description, category, image, rating } = product

  const handleAddToCart = () => {
    onAddToCart(product)
  }

  const truncateText = (text, maxLength) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <div className="product-card">
      <div className="product-image-container">
        <img 
          src={image} 
          alt={title}
          className="product-image"
          loading="lazy"
        />
        <div className="product-category">{category}</div>
      </div>
      
      <div className="product-info">
        <h3 className="product-title" title={title}>
          {truncateText(title, 60)}
        </h3>
        
        <p className="product-description" title={description}>
          {truncateText(description, 100)}
        </p>
        
        <div className="product-rating">
          <span className="rating-stars">
            {'★'.repeat(Math.floor(rating?.rate || 0))}
            {'☆'.repeat(5 - Math.floor(rating?.rate || 0))}
          </span>
          <span className="rating-count">({rating?.count || 0})</span>
        </div>
        
        <div className="product-footer">
          <div className="product-price">
            ${price?.toFixed(2)}
          </div>
          <button 
            onClick={handleAddToCart}
            className="add-to-cart-btn"
            aria-label={`Add ${title} to cart`}
          >
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  )
}

export default ProductCard
