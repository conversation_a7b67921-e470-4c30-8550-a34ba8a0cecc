/* CartItem.css */
.cart-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  background: white;
  transition: all 0.2s ease;
}

.cart-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cart-item:last-child {
  margin-bottom: 0;
}

.cart-item-image {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
  border-radius: 6px;
  overflow: hidden;
  background: #f8fafc;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 0.25rem;
}

.cart-item-details {
  flex: 1;
  min-width: 0;
}

.cart-item-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.cart-item-price {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  flex-shrink: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 0.25rem;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: white;
  color: #374151;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.quantity-btn:hover {
  background: #667eea;
  color: white;
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-display {
  min-width: 24px;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.cart-item-total {
  font-weight: 700;
  color: #059669;
  font-size: 0.9rem;
}

.remove-btn {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.remove-btn:hover {
  background: #fef2f2;
  transform: scale(1.1);
}

.remove-btn:active {
  transform: scale(0.95);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .cart-item {
    gap: 0.75rem;
    padding: 0.75rem;
  }
  
  .cart-item-image {
    width: 50px;
    height: 50px;
  }
  
  .cart-item-title {
    font-size: 0.85rem;
  }
  
  .cart-item-price {
    font-size: 0.75rem;
  }
  
  .quantity-btn {
    width: 26px;
    height: 26px;
    font-size: 0.8rem;
  }
  
  .quantity-display {
    min-width: 20px;
    font-size: 0.85rem;
  }
  
  .cart-item-total {
    font-size: 0.85rem;
  }
  
  .remove-btn {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .cart-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .cart-item-details {
    order: 1;
  }
  
  .cart-item-image {
    order: 0;
    width: 80px;
    height: 80px;
    align-self: center;
  }
  
  .cart-item-controls {
    order: 2;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .quantity-controls {
    order: 1;
  }
  
  .cart-item-total {
    order: 2;
    font-size: 1rem;
  }
  
  .remove-btn {
    order: 3;
  }
}
