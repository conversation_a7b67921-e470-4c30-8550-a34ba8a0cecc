/* Cart.css */
.cart {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: calc(100vh - 4rem);
  display: flex;
  flex-direction: column;
}

.cart-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.cart-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.cart-icon {
  font-size: 1.25rem;
}

.cart-count {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: #9ca3af;
  text-align: center;
}

.empty-cart-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-cart p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.empty-cart-subtitle {
  font-size: 0.9rem !important;
  opacity: 0.7;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  max-height: 400px;
}

.cart-summary {
  border-top: 1px solid #e5e7eb;
  padding: 1.5rem;
  background: #f9fafb;
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.total-label {
  color: #374151;
}

.total-amount {
  color: #059669;
  font-size: 1.5rem;
}

.checkout-btn {
  width: 100%;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

.checkout-btn:active {
  transform: translateY(0);
}

/* Mobile responsiveness */
@media (max-width: 1024px) {
  .cart {
    max-height: none;
  }
  
  .cart-items {
    max-height: none;
    overflow-y: visible;
  }
}

@media (max-width: 768px) {
  .cart-header {
    padding: 1.25rem;
  }
  
  .cart-title {
    font-size: 1.25rem;
  }
  
  .cart-items {
    padding: 0.75rem;
  }
  
  .cart-summary {
    padding: 1.25rem;
  }
  
  .cart-total {
    font-size: 1.1rem;
  }
  
  .total-amount {
    font-size: 1.3rem;
  }
  
  .checkout-btn {
    padding: 0.875rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .empty-cart {
    padding: 2rem 1rem;
  }
  
  .empty-cart-icon {
    font-size: 2.5rem;
  }
  
  .empty-cart p {
    font-size: 1rem;
  }
}
