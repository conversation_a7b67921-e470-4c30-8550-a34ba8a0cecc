/* Header.css */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.header-icon {
  font-size: 2rem;
}

.header-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .header {
    padding: 1.5rem 0;
  }
  
  .header-content {
    padding: 0 1rem;
  }
  
  .header-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .header-icon {
    font-size: 1.5rem;
  }
  
  .header-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 1.75rem;
  }
  
  .header-subtitle {
    font-size: 0.9rem;
  }
}
